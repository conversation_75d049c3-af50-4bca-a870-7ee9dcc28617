interface IdLink {
  [key: number]: string;
}

interface NativeURLS {
  [key: string]: string;
}

interface WapURLS {
  [key: string]: string;
}

interface ApiObject {
  name: string;
  api: string;
  redirect?: { [key: string]: string };
  link?: Array<Array<[string, string]>>;
  ext?: { [key: string]: any };
  oneValue?: { [key: string]: boolean };
  apiLink?: { [key: string]: Array<Array<[string, string]>> };
  openApi?: string;
}

interface S3js {
  [key: string]: ApiObject;
}

class PortalM3Utils {
  onloadApi: { [key: string]: any } = {};
  idLink: IdLink = {
    1: 'collaboration', // 协同应用
    2: 'form', // 表单
    3: 'doc', // 知识管理
    4: 'edoc', // 公文
    6: 'meeting', // 会议
    7: 'bulletin', // 公告
    8: 'news', // 新闻
    9: 'bbs', // 讨论
    10: 'inquiry', // 调查
    11: 'calendar',
    17: 'hr', // 人力资源
    26: 'office', // 综合办公
    27: 'agent',
    30: 'taskmanage',
    36: 'attendance', // 签到
    39: 'cip',
    40: 'show', // 大秀
    42: 'footprint',
    43: 'biz', // 业务生成器
    44: 'commons', // 公共资源
    45: 'workflow', // 工作流
    47: 'unflowform', // 无流程表单
    48: 'formqueryreport', // 表单查询统计
    49: 'cmp', // cmp
    51: 'dee', // dee模块
    52: 'application', // 应用模块
    53: 'm3commons', // 公共资源(m3)
    54: 'login', // 登陆
    55: 'message', // 消息模块
    56: 'my', // 我的模块
    57: 'search', // 搜索模块
    58: 'todo', // 待办模块
    59: 'fullsearch', // 全文检索
    60: 'mycollection', // 我的收藏
    61: 'uc', // UC
    62: 'addressbook', // 通讯录
    63: 'seeyonreport', // 帆软报表
    65: 'portal', // H5门户
    70: 'vreport', // 报表中心
    66: 'cap4', // cap4表单
    67: 'cap4biz', // 应用管理
    68: 'fileReport', // 文件报表
    69: 'excelreport', // excel报表
    71: 'memorabilia', // 大事记
    72: 'ai', // AI智能插件
    73: 'querybtn', // cap4自定义控件-查询统计按钮
    74: 'invoice', // cap4自定义控件电子发票
    75: 'formcreditqueryctrl', // cap4自定义控件-企业征信查询
    76: 'ocrbtn', // cap4自定义控件-ocr图像识别
    77: 'formwordinjectionctrl', // 表单自定义控件-word套红控件
    78: 'formhandwritectrl', // 签章
    79: 'xiaoz', // 小智
    80: 'capqrcode', // 二维码(cap)
    81: 'templateApprove', // 模板审批(cap)
    82: 'cap4report', // cap4统计(cap)
    83: 'cap4business', // cap4业务包门户(cap)
    84: 'cap4query', // cap4查询(cap)
    85: 'cap4unflow', // cap4无流程列表
    86: 'cwidgetnewform', // cap4自定义控件 新建表单
    87: 'cwidgetviewform', // cap4自定义控件 查看表单
    88: 'capextend', // cap4扩展控件
    89: 'ctripcityform', // 携程控件
    90: 'inspect', // cmp 体检
    91: 'cap4todolist', // cap4待办
    92: 'trustdo', // 信任度
    94: 'leaderagenda', // 领导日程
    127: 'internalmail', // 内部邮箱
  };
  NativeURLS: NativeURLS = {
    collaboration: 'http://collaboration.v5.cmp/v1.0.0', // 协同应用
    cap4: 'http://cap4.v5.cmp/v1.0.0', // 表单
    cap4report: 'http://cap4report.v5.cmp/v1.0.0', // cap4统计，统计详情，历史列表
    cap4business: 'http://cap4business.v5.cmp/v1.0.0', // cap4门户首页
    cap4query: 'http://cap4query.v5.cmp/v1.0.0', // cap4查询
    cap4unflow: 'http://cap4unflow.v5.cmp/v1.0.0', // cap4无流程列表
    cap4todolist: 'http://cap4todolist.v5.cmp/v1.0.0', // 流程待办
    news: 'http://news.v5.cmp/v1.0.0', // 文化建设-新闻
    bulletin: 'http://bulletin.v5.cmp/v1.0.0', // 文化建设-公告
    bbs: 'http://bbs.v5.cmp/v1.0.0', // 文化建设-讨论
    inquiry: 'http://inquiry.v5.cmp/v1.0.0', // 文化建设-调查
    show: 'http://show.v5.cmp/v1.0.0', // 文化建设-大秀(享空间)
    doc: 'http://doc.v5.cmp/v1.0.0', // 知识管理（知识社区）
    vreport: 'http://vreport.v5.cmp/v1.0.0', // 统计穿透
    unflowform: 'http://unflowform.v5.cmp/v1.0.0', // cap3无流程
    portal: 'http://portal.v5.cmp/v1.0.0', // v5门户
    todo: 'http://todo.m3.cmp/v1.0.0',
    edoc: 'http://edoc.v5.cmp/v1.0.0',
    agent: 'http://agent.v5.cmp/v1.0.0',
    meeting: 'http://meeting.v5.cmp/v1.0.0',
    calendar: 'http://calendar.v5.cmp/v1.0.0',
    leaderagenda: 'http://leaderagenda.v5.cmp/v1.0.0',
    mycollection: 'http://mycollection.v5.cmp/v1.0.0',
    footprint: 'http://footprint.v5.cmp/v1.0.0',
    search: 'http://search.m3.cmp/v1.0.0',
    formqueryreport: 'http://formqueryreport.v5.cmp/v1.0.0',
    taskmanage: 'http://taskmanage.v5.cmp/v1.0.0',
    hr: 'http://hr.v5.cmp/v1.0.0',
    attendance: 'http://attendance.v5.cmp/v1.0.0',
    cip: 'http://cip.v5.cmp/v1.0.0',
    internalmail: 'http://internalmail.v5.cmp/v1.0.0',
  };
  WapURLS: WapURLS = {
    collaboration: '/m3/apps/v5/collaboration', // 协同应用
    cap4: '/m3/apps/v5/cap4', // 表单
    cap4report: '/m3/apps/v5/cap4report', // cap4统计，统计详情，历史列表
    cap4business: '/m3/apps/v5/cap4business', // cap4门户首页
    cap4query: '/m3/apps/v5/cap4query', // cap4查询
    cap4unflow: '/m3/apps/v5/cap4unflow', // cap4无流程列表
    cap4todolist: '/m3/apps/v5/cap4todolist', // 流程待办
    news: '/m3/apps/v5/news', // 文化建设-新闻
    bulletin: '/m3/apps/v5/bulletin', // 文化建设-公告
    bbs: '/m3/apps/v5/bbs', // 文化建设-讨论
    inquiry: '/m3/apps/v5/inquiry', // 文化建设-调查
    show: '/m3/apps/v5/show', // 文化建设-大秀(享空间)
    doc: '/m3/apps/v5/doc', // 知识管理（知识社区）
    vreport: '/m3/apps/v5/vreport', // 统计穿透
    unflowform: '/m3/apps/v5/unflowform', // cap3无流程
    portal: '/m3/apps/v5/portal', // v5门户
    todo: '/m3/apps/m3/todo',
    edoc: '/m3/apps/v5/edoc',
    agent: '/m3/apps/v5/agent',
    meeting: '/m3/apps/v5/meeting',
    calendar: '/m3/apps/v5/calendar',
    leaderagenda: '/m3/apps/v5/leaderagenda',
    mycollection: '/m3/apps/v5/mycollection',
    footprint: '/m3/apps/v5/footprint',
    search: '/m3/apps/m3/search',
    formqueryreport: '/m3/apps/v5/formqueryreport',
    taskmanage: '/m3/apps/v5/taskmanage',
    hr: '/m3/apps/v5/hr',
    attendance: '/m3/apps/v5/attendance',
    cip: '/m3/apps/v5/cip',
    internalmail: '/m3/apps/v5/internalmail',
  };
  s3js: S3js = {
    collaboration: { name: 'collaboration_m_api.s3js', api: 'collApi',
      oneValue: { jumpToColSummary: true,  openApp: true},
      apiLink: {
        "jumpToColSummary": [
          [['id', 'id']]
          // [['comeFrom', 'comeFrom']]
        ],
        "openApp": [
          [['type', 'type']],
          [['backUrl', 'backUrl']],
          [['option', 'option']],
          [['obj', 'obj']],
        ],
        "newCollByComi": [
          [['options', 'options']],
        ]
      },
     }, // 协同应用
    cap4: {
      name: 'cap4_m_api.s3js',
      api: 'cap4Api',
      redirect: { viewUnflow: 'openUnFlow' },
      apiLink: {},
    }, // 表单
    cap4report: {
      name: 'cap4report_m_api.s3js',
      api: 'cap4ReportApi',
      redirect: { open: 'openFormQueryReport' },
      link: [
        [
          ['appId', 'designId'],
          ['pathId', 'pathId'],
        ],
      ],
      ext: { type: 3 },
    }, // cap4统计，统计详情，历史列表
    cap4business: {
      name: 'cap4business_m_api.s3js',
      api: 'cap4BusinessApi',
      redirect: { open: 'openBizInfo' },
    }, // cap4门户首页
    cap4query: {
      name: 'cap4query_m_api.s3js',
      api: 'cap4QueryApi',
      redirect: { open: 'openFormQueryReport' },
      link: [
        [
          ['appId', 'designId'],
          ['pathId', 'pathId'],
        ],
      ],
      ext: { type: 4 },
    }, // cap4查询
    cap4unflow: {
      name: 'cap4unflow_m_api.s3js',
      api: 'cap4UnflowApi',
      redirect: { open: 'openUnFlowList' },
    }, // cap4无流程列表
    cap4todolist: {
      name: 'cap4todolist_m_api.s3js',
      api: 'cap4TodoListApi',
      redirect: { open: 'openTodoList' },
    }, // 流程待办
    news: { 
      name: 'news_m_api.s3js', 
      api: 'newsApi', 
      oneValue: { jumpToNews: true, openApp: true },
      apiLink: {
        "jumpToNews": [
          [['id', 'id']],
          [['comeFrom', 'comeFrom']]
          // [['backUrl', 'backUrl']],
          // [['option', 'option']],
          // [['obj', 'obj']],
        ],
        "openApp": [
          [['type', 'type']],
          [['backUrl', 'backUrl']],
          [['option', 'option']],
          [['obj', 'obj']],
        ],
      }, 
    }, // 文化建设-新闻
    bulletin: { name: 'bulletin_m_api.s3js', api: 'bulletinApi',
      oneValue: { jumpToBulletin: true, openApp: true},
      apiLink: {
        "jumpToBulletin": [
          [['id', 'id']],
          [['comeFrom', 'comeFrom']]
        ],
        "openApp": [
          [['type', 'type']],
          [['backUrl', 'backUrl']],
          [['option', 'option']],
          [['obj', 'obj']],
        ],
      },

     }, // 文化建设-公告
    bbs: { 
      name: 'bbs_m_api.s3js', 
      api: 'bbsApi',
      oneValue: { openApp: true },
      apiLink: {
        openApp: [
          [['type', 'type']],
          [['backUrl', 'backUrl']],
          [['option', 'option']],
          [['obj', 'obj']],
        ],
      },
    }, // 文化建设-讨论
    inquiry: { 
      name: 'inquiry_m_api.s3js', 
      api: 'inquiryApi',
      oneValue: { openApp: true },
      apiLink: {
        openApp: [
          [['type', 'type']],
          [['backUrl', 'backUrl']],
          [['option', 'option']],
          [['obj', 'obj']],
        ],
      },
     }, // 文化建设-调查
    show: { name: 'show_m_api.s3js', api: 'showApi' }, // 文化建设-大秀(享空间)
    doc: { 
      name: 'doc_m_api.s3js',
      api: 'docApi',
      oneValue: { openApp: true },
      apiLink: {
        openApp: [
          [['type', 'type']],
          [['backUrl', 'backUrl']],
          [['option', 'option']],
          [['obj', 'obj']],
        ],
      },
    }, // 知识管理（知识社区）
    vreport: { name: 'vreport_m_api.s3js', api: 'vreportApi' }, // v5报表
    unflowform: {
      name: 'unflowform_m_api.s3js',
      api: 'unflowformApi',
      oneValue: { openApp: true },
      apiLink: {
        openApp: [
          [['type', 'type']],
          [['backUrl', 'backUrl']],
          [['option', 'option']],
          [['obj', 'obj']],
        ],
      },
    },
    portal: { name: 'portal_m_api.s3js', api: 'vPortalApi' }, // 文化建设-新闻
    todo: { name: 'm3-todo-api.js', api: 'm3TodoApi' }, // 协同待办列表
    edoc: { 
      name: 'edoc_m_api.s3js', 
      api: 'edocApi', 
      oneValue: { openApp: true },
      apiLink: {
        openApp: [
          [['type', 'type']],
          [['backUrl', 'backUrl']],
          [['option', 'option']],
          [['obj', 'obj']],
        ],
      },
    },
    agent: { name: 'agent_m_api.s3js', api: 'agentApi' },
    meeting: { 
      name: 'meeting_m_api.s3js', 
      api: 'meetingApi',
      oneValue: { openApp: true },
      apiLink: {
        openApp: [
          [['type', 'type']],
          [['backUrl', 'backUrl']],
          [['option', 'option']],
          [['obj', 'obj']],
        ],
      },
    },
    calendar: { 
      name: 'calendar_m_api.s3js', 
      api: 'calendarApi',
      oneValue: { openApp: true },
      apiLink: {
        openApp: [
          [['type', 'type']],
          [['backUrl', 'backUrl']],
          [['option', 'option']],
          [['obj', 'obj']],
        ],
      }
    },
    leaderagenda: { name: 'leaderagenda_m_api.s3js', api: 'leaderagendaApi' },
    mycollection: { name: 'mycollection_m_api.s3js', api: 'mycollectionApi' },
    footprint: { name: 'footprint_m_api.s3js', api: 'footprintApi' },
    search: { name: 'm3-search-api.js', api: 'm3SearchApi' },
    formqueryreport: { name: 'formqueryreport_m_api.s3js', api: 'formqApi' },
    taskmanage: { 
      name: 'taskmanage_m_api.s3js', 
      api: 'taskmanageApi',
      oneValue: { openApp: true },
      apiLink: {
        openApp: [
          [['type', 'type']],
          [['backUrl', 'backUrl']],
          [['option', 'option']],
          [['obj', 'obj']],
        ],
      },
    },
    hr: { name: 'hr_m_api.s3js', api: 'hrApi' },
    attendance: { name: 'attendance_m_api.s3js', api: 'attendanceApi' },
    cip: { name: 'cip_m_api.s3js', api: 'cipApi' },
    internalmail: { name: 'internalmail_m_api.s3js', api: 'internalmailApi' },
  };

  getApiFun(api: any, apiObject: ApiObject): Function {
    let apiFun: Function | null = null;
    const link =
      apiObject.apiLink && apiObject.apiLink[apiObject.openApi!]
        ? apiObject.apiLink[apiObject.openApi!]
        : apiObject.link;
    const oneValue = apiObject.oneValue && apiObject.oneValue[apiObject.openApi!] ? true : false;
    if (!link) {
      apiFun = function (inputParams: any) {
        api[apiObject.openApi!].apply(api, [inputParams]);
      };
    } else {
      apiFun = function (inputParams: any) {
        const params: any[] = [];
        for (let i = 0; i < link.length; i++) {
          const link1 = link[i];
          if (oneValue && link1.length == 1) {
            params.push(inputParams[link1[0][1]]);
          } else {
            const param = Object.assign({}, inputParams); // 参数拷贝。
            for (let j = 0; j < link1.length; j++) {
              const links1 = link1[j];
              param[links1[0]] = inputParams[links1[1]];
            }
            if (apiObject.ext) {
              Object.assign(param, apiObject.ext);
            }
            params.push(param);
          }
        }
        api[apiObject.openApi!].apply(api, params);
      };
    }
    return apiFun;
  }

  loadApiAsync(url: string, apiObject: ApiObject, callback: Function): void {
    if (this.onloadApi[apiObject.api]) {
      callback(this.getApiFun(this.onloadApi[apiObject.api], apiObject));
    } else {
      const scriptEle = document.createElement('script');
      scriptEle.type = 'text/javascript';
      scriptEle.src = url;
      scriptEle.onload = () => {
        this.onloadApi[apiObject.api] = (window as any)[apiObject.api];
        callback(this.getApiFun(this.onloadApi[apiObject.api], apiObject));
      };
      document.body.appendChild(scriptEle);
    }
  }

  getApi(inputParams: any, fn: Function): void {
    window.localStorage.ctxPath = "/seeyon"
    if (!inputParams) return;
    if (!inputParams.appId) {
      // 新门户跳转
      if (inputParams.newSpace === true && inputParams.spaceId) {
        inputParams.params = JSON.parse(JSON.stringify(inputParams));
        inputParams.appId = 66;
        inputParams.openApi = 'openBizInfoBySpaceId';
      }
      // 旧门户跳转
      if (inputParams.newSpace === false && inputParams.bizId) {
        inputParams.params = JSON.parse(JSON.stringify(inputParams));
        inputParams.appId = 66;
        inputParams.openApi = 'openBizInfoOld';
      }
    }
    this.cmpReady(() => {
      const apiName = this.idLink[inputParams.appId];
      const apiObject = this.s3js[apiName];
      if(!apiObject) {
        fn(null);
      }
      apiObject.openApi = inputParams.openApi;
      // 如果api入口有重定向
      if (apiObject.redirect && apiObject.redirect[apiObject.openApi!]) {
        apiObject.openApi = apiObject.redirect[apiObject.openApi!];
      }
      if (apiObject) {
        this.loadApiAsync(this.getHost(apiName) + '/' + apiObject.name, apiObject, fn);
      }
    });
  }

  cmpReady(fn: Function): void {
    if ((window as any).cmp) {
      (window as any).cmp.ready(() => {
        fn((window as any).cmp);
      });
    } else {
      const CMPHost = this.getCmpHost();

      const scriptEle1 = document.createElement('script');
      scriptEle1.type = 'text/javascript';
      scriptEle1.src = CMPHost + 'cmp-i18n.js';
      document.body.appendChild(scriptEle1);

      const scriptEle2 = document.createElement('script');
      scriptEle2.type = 'text/javascript';
      scriptEle2.src = CMPHost + 'cmp.js';
      scriptEle2.onload = () => {
        (window as any).cmp.ready(() => {
          const scriptEle3 = document.createElement('script');
          scriptEle3.type = 'text/javascript';
          scriptEle3.src = CMPHost + 'cmp-asyncLoad.js';
          document.body.appendChild(scriptEle3);
          fn((window as any).cmp);
        });
      };
      document.body.appendChild(scriptEle2);
    }
  }

  getHost(apiName: string): string {
    return '/seeyon' + this.WapURLS[apiName];
  }

  getCmpHost(): string {
    const CMPHost = '/seeyon/m3/cmp/js/';
    return CMPHost;
  }
}

export default new PortalM3Utils();