<template>
  <div class="create-meeting-card">
    <div class="card-header">
      <!-- <img src="@/assets/calendar-icon.png" alt="Calendar Icon" class="calendar-icon" /> -->
      <i class="iconfont ai-icon-shijiananpai create-icon"></i>
      <span class="header-text">发起会议</span>
    </div>
    <div class="card-content">
      <div class="content-item">
        <span class="label">会议主题:</span>
        <span class="value">Ai应用部例行早会</span>
      </div>
      <div class="content-item">
        <span class="label">会议时间:</span>
        <span class="value">2025-07-31 09:00-10:00</span>
      </div>
      <div class="content-item">
        <span class="label">会议地点:</span>
        <span class="value">新川1401、北京203</span>
        <span class="iconfont ai-icon-you more-icon"></span>
      </div>
      <div class="content-item">
        <span class="label">与会人:</span>
        <span class="value">
          王维、李昂等12人
        </span>
        <span class="iconfont ai-icon-you more-icon"></span>
      </div>
      <div class="content-item reminder">
        <span class="label">会议提醒</span>
        <label class="toggle-container">
          <input type="checkbox" checked>
          <span class="slider round"></span>
        </label>
      </div>
    </div>
    <div class="card-footer">
      <button class="cancel-btn">取消</button>
      <button class="submit-btn">发起</button>
    </div>
  </div>
</template>

<script setup lang="ts">
// 这里可以添加你的 TypeScript 逻辑
</script>

<style scoped lang="scss">
.create-meeting-card {
  width: 100%;
  max-width: 500px;
  background: linear-gradient(180deg, #FFFFFF 0%, #E6F0FF 100%);
  border-radius: 10px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  height: 348px;
  box-sizing: border-box;
  background: url('../../assets/images/header-background.png') no-repeat;
  background-size: 100% 100%;
  .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    height: 22px;
    .create-icon {
      font-size: 18px;
      color: #4379FF;
      margin-right: 4px;
    }
  }
  .calendar-icon {
    width: 24px;
    height: 24px;
    margin-right: 8px;
  }
  
  .header-text {
    font-size: 14px;
    font-weight: 600;
    color: rgba(0, 0, 0, 1);
    color: var(--theme-font-color0, rgba(0, 0, 0, 1));
  }
  
  .card-content {
    margin-bottom: 20px;
  }
  
  .content-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 16px;
    color: #666;
    height: 46px;
    font-size: 14px;
    font-size: var(--theme-font-size1, 14px);
    position: relative;
    &::after {
      content: '';
      position: absolute;
      left: 0;
      bottom: 0;
      display: block;
      height: 1px;
      width: 100%;
      background-color: rgba(0, 0, 0, 0.1);
      transform: scaleY(0.5);
    }
  }
  
  .label {
    margin-right: 8px;
    color: rgba(0, 0, 0, 0.6);
    color: var(--theme-font-color2, rgba(0, 0, 0, 0.6));
  }
  
  .value {
    flex: 1;
    color: rgba(0, 0, 0, 0.9);
    color: var(--theme-font-color1, rgba(0, 0, 0, 0.9));
  }
  
  .user-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    margin-right: 8px;
  }
  
  .reminder .toggle-container {
    display: inline-block;
    position: relative;
    width: 44px;
    height: 24px;
  }
  
  .reminder .toggle-container input {
    opacity: 0;
    width: 0;
    height: 0;
  }
  
  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: .4s;
    transition: .4s;
  }
  
  .slider:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 20px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    -webkit-transition: .4s;
    transition: .4s;
  }
  
  input:checked + .slider {
    background-color: #4379FF;
  }
  
  input:focus + .slider {
    box-shadow: 0 0 1px #4379FF;
  }
  
  input:checked + .slider:before {
    transform: translateX(20px);
  }
  
  /* Rounded sliders */
  .slider.round {
    border-radius: 34px;
  }
  
  .slider.round:before {
    border-radius: 50%;
  }
  
  .card-footer {
    display: flex;
    justify-content: space-between;
    gap: 12px;
  }
  
  .cancel-btn, .submit-btn {
    // width: 48%;

    flex: 1;
    height: 32px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    cursor: pointer;
  }
  
  .cancel-btn {
    background: #fff;
    color: rgba(0, 0, 0, 0.9);
    color: var(--theme-font-color1, rgba(0, 0, 0, 0.9));
    border: 1px solid #D8DADF;
  }
  
  .submit-btn {
    background: linear-gradient(89.79deg, #677EFF 0.31%, #4379FF 53.97%, #6EBBFF 99.07%);
    color: #fff;
  }
}

</style>